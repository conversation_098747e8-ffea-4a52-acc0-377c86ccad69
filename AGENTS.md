# AGENTS.md

This file provides guidance to agents when working with code in this repository.

## Non-Obvious Build & Test Commands

- **Server startup**: run `bash start.sh start`
- **Server stop**: run `bash start.sh stop`
- **Server status check**: run `bash start.sh status`
- **Architecture versions**: Set `VLLM_USE_V1=0` for V0 (monolithic) or `VLLM_USE_V1=1` for V1 (separated concerns)
- **Turing optimizations**: This branch (`sm75`) contains Turing attention backend for SM75 GPUs (RTX 2080 Ti)

## Code Style & Patterns

- **Type checking exclusions**: My<PERSON> excludes `vllm/model_executor/parallel_utils/` and `vllm/model_executor/models/` (TODO for Megatron/HuggingFace)
- **Ruff ignores**: Specific files in `vllm/third_party/` and Python 3.8 typing rules for attention/core/engine/worker/executor modules
- **Testing markers**: Use pytest markers like `@pytest.mark.slow_test`, `@pytest.mark.distributed`, `@pytest.mark.skip_v1`
- **Model interface**: All models use `__init__(self, *, vllm_config: VllmConfig, prefix: str = "")` signature

## Critical Development Notes

- **Environment dependency**: Server MUST be started from within conda environment for proper CUDA detection
- **Large context**: This branch optimized for 131072 tokens max context
- **Port configuration**: Server runs on port 8000 with OpenAI-compatible API
- **Log location**: Server logs available in `vllm_server.log` under parent dir of source code root
- **CRITICAL**: The python script using vLLM cannot be run when current directory is source code root - must change the current directory before running the script.
