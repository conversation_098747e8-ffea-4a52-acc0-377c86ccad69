#!/usr/bin/env python3
# SPDX-License-Identifier: Apache-2.0
# SPDX-FileCopyrightText: Copyright contributors to the vLLM project

"""
集成测试：验证V1架构在SM75 GPU上的完整工作流程

此脚本测试以下场景：
1. SM75 GPU上V1引擎的启动流程
2. 自动特性降级（BFloat16、FP8等）
3. 注意力后端选择
4. 基本推理功能
5. 性能基准测试
"""

import os
import sys
import time
import torch
import logging
from unittest.mock import patch, MagicMock
from contextlib import contextmanager

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


@contextmanager
def mock_sm75_environment():
    """模拟SM75 GPU环境的上下文管理器"""
    mock_capability = MagicMock()
    mock_capability.major = 7
    mock_capability.minor = 5

    with patch.object(torch.cuda, 'is_available', return_value=True), \
         patch.object(torch.cuda, 'device_count', return_value=1), \
         patch.object(torch.cuda, 'get_device_capability', return_value=(7, 5)), \
         patch.object(torch.cuda, 'get_device_name', return_value='NVIDIA RTX 2080 Ti'), \
         patch('vllm.platforms.cuda.CudaPlatform.get_device_capability', return_value=mock_capability), \
         patch('vllm.platforms.cuda.CudaPlatform.get_device_name', return_value='NVIDIA RTX 2080 Ti'), \
         patch('vllm.platforms.cuda.CudaPlatform.is_cuda', return_value=True):

        yield


def test_v1_engine_creation_sm75():
    """测试SM75上V1引擎的创建"""
    logger.info("测试SM75上V1引擎创建...")

    with mock_sm75_environment():
        import vllm.envs as envs

        # 设置V1引擎
        os.environ['VLLM_USE_V1'] = '1'
        envs.set_vllm_use_v1(True)

        try:
            from vllm.engine.arg_utils import EngineArgs

            # 创建引擎参数
            args = EngineArgs(
                model="facebook/opt-125m",
                dtype="float16",  # SM75不支持BFloat16，但应该自动降级
                max_model_len=512,
                trust_remote_code=True
            )

            # 创建模型配置
            model_config = args.create_model_config()

            # 测试V1支持性检查
            is_v1_supported = args._is_v1_supported_oracle(model_config)

            logger.info(f"V1支持性检查结果: {is_v1_supported}")
            assert is_v1_supported, "SM75应该支持V1引擎（带有限制）"

            logger.info("✓ SM75 V1引擎创建测试通过")

        except Exception as e:
            logger.error(f"SM75 V1引擎创建失败: {e}")
            raise
        finally:
            # 清理环境变量
            if 'VLLM_USE_V1' in os.environ:
                del os.environ['VLLM_USE_V1']
            envs.set_vllm_use_v1(False)


def test_attention_backend_selection_sm75():
    """测试SM75上的注意力后端选择"""
    logger.info("测试SM75注意力后端选择...")

    with mock_sm75_environment():
        from vllm.platforms.cuda import CudaPlatform

        # 测试不同配置的后端选择
        test_cases = [
            {
                'head_size': 64,
                'dtype': torch.float16,
                'kv_cache_dtype': None,
                'expected_backend': 'TuringAttentionBackendV1'
            },
            {
                'head_size': 64,
                'dtype': torch.bfloat16,  # 应该降级
                'kv_cache_dtype': None,
                'expected_backend': 'TuringAttentionBackendV1'
            },
            {
                'head_size': 64,
                'dtype': torch.float16,
                'kv_cache_dtype': 'fp8',  # 应该降级
                'expected_backend': 'TuringAttentionBackendV1'
            }
        ]

        for i, case in enumerate(test_cases):
            logger.info(f"测试案例 {i+1}: {case}")

            backend_cls = CudaPlatform.get_attn_backend_cls(
                selected_backend=None,
                use_v1=True,
                use_mla=False,
                has_sink=False,
                **{k: v for k, v in case.items() if k != 'expected_backend'}
            )

            expected = case['expected_backend']
            logger.info(f"选择的后端: {backend_cls}")
            assert expected in backend_cls, f"期望后端 {expected}，实际得到 {backend_cls}"

        logger.info("✓ SM75注意力后端选择测试通过")


def test_feature_degradation_sm75():
    """测试SM75上的特性降级"""
    logger.info("测试SM75特性降级...")

    with mock_sm75_environment():
        from vllm.platforms.cuda import CudaPlatform

        # 测试BFloat16降级
        with patch('vllm.platforms.cuda.logger') as mock_logger:
            backend_cls = CudaPlatform.get_attn_backend_cls(
                selected_backend=None,
                head_size=64,
                dtype=torch.bfloat16,  # SM75不支持
                kv_cache_dtype=None,
                block_size=16,
                use_v1=True,
                use_mla=False,
                has_sink=False
            )

            # 应该有降级警告
            warning_calls = [call for call in mock_logger.warning_once.call_args_list
                           if 'BFloat16' in str(call) or 'Float16' in str(call)]
            assert len(warning_calls) > 0, "应该有BFloat16降级警告"

        # 测试FP8降级
        with patch('vllm.platforms.cuda.logger') as mock_logger:
            backend_cls = CudaPlatform.get_attn_backend_cls(
                selected_backend=None,
                head_size=64,
                dtype=torch.float16,
                kv_cache_dtype='fp8',  # SM75不支持
                block_size=16,
                use_v1=True,
                use_mla=False,
                has_sink=False
            )

            # 应该有降级警告
            warning_calls = [call for call in mock_logger.warning_once.call_args_list
                           if 'FP8' in str(call)]
            assert len(warning_calls) > 0, "应该有FP8降级警告"

        logger.info("✓ SM75特性降级测试通过")


def test_turing_backend_availability():
    """测试Turing后端可用性"""
    logger.info("测试Turing后端可用性...")

    with mock_sm75_environment():
        from vllm.attention.backends.turing_attn import is_turing_backend_available_for_v1

        # 模拟Turing后端可用
        with patch('vllm.attention.backends.turing_attn.TuringAttentionBackendV1'):
            available = is_turing_backend_available_for_v1()
            logger.info(f"Turing后端可用性: {available}")
            assert available, "Turing后端应该可用"

        logger.info("✓ Turing后端可用性测试通过")


def run_performance_benchmark():
    """运行性能基准测试"""
    logger.info("运行SM75性能基准测试...")

    with mock_sm75_environment():
        import vllm.envs as envs

        # 设置V1引擎
        os.environ['VLLM_USE_V1'] = '1'
        envs.set_vllm_use_v1(True)

        try:
            from vllm.engine.arg_utils import EngineArgs

            # 创建引擎参数（小模型用于测试）
            args = EngineArgs(
                model="facebook/opt-125m",
                dtype="float16",
                max_model_len=128,  # 小序列长度用于快速测试
                max_num_seqs=2,
                trust_remote_code=True
            )

            # 创建模型配置
            model_config = args.create_model_config()

            # 测试V1支持性
            is_v1_supported = args._is_v1_supported_oracle(model_config)

            if is_v1_supported:
                logger.info("✓ SM75 V1引擎配置验证通过")

                # 记录基准信息
                benchmark_info = {
                    'compute_capability': 'SM75',
                    'v1_engine_enabled': True,
                    'expected_limitations': [
                        'BFloat16 not supported',
                        'FP8 KV cache not supported',
                        'Some SM80+ optimizations disabled'
                    ],
                    'recommended_backend': 'TuringAttentionBackendV1'
                }

                logger.info("基准测试信息:")
                for key, value in benchmark_info.items():
                    logger.info(f"  {key}: {value}")

                return benchmark_info
            else:
                raise AssertionError("SM75 V1引擎支持检查失败")

        except Exception as e:
            logger.error(f"性能基准测试失败: {e}")
            raise
        finally:
            # 清理环境变量
            if 'VLLM_USE_V1' in os.environ:
                del os.environ['VLLM_USE_V1']
            envs.set_vllm_use_v1(False)


def main():
    """主测试函数"""
    logger.info("开始SM75 V1架构兼容性集成测试")
    logger.info("=" * 60)

    test_results = []

    try:
        # 运行各个测试
        test_v1_engine_creation_sm75()
        test_results.append(("V1引擎创建", True))

        test_attention_backend_selection_sm75()
        test_results.append(("注意力后端选择", True))

        test_feature_degradation_sm75()
        test_results.append(("特性降级", True))

        test_turing_backend_availability()
        test_results.append(("Turing后端可用性", True))

        benchmark_info = run_performance_benchmark()
        test_results.append(("性能基准测试", True))

        # 总结测试结果
        logger.info("=" * 60)
        logger.info("测试结果总结:")

        for test_name, passed in test_results:
            status = "✓ 通过" if passed else "✗ 失败"
            logger.info(f"  {test_name}: {status}")

        logger.info("=" * 60)
        logger.info("🎉 所有SM75 V1兼容性测试通过！")
        logger.info("修复验证成功：SM75现在支持V1架构（带有限制）")

        return True

    except Exception as e:
        logger.error(f"测试失败: {e}")
        logger.info("=" * 60)
        logger.info("❌ SM75 V1兼容性测试失败")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)