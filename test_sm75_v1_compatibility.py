#!/usr/bin/env python3
# SPDX-License-Identifier: Apache-2.0
# SPDX-FileCopyrightText: Copyright contributors to the vLLM project

"""
测试V1架构在SM75（Turing）GPU上的兼容性修复。

此测试文件验证以下修复：
1. SM75硬件不再抛出NotImplementedError
2. 正确降级不支持的特性（如BFloat16、FP8）
3. 正确选择Turing注意力后端
4. 性能警告和日志输出正确
"""

import os
import pytest
import torch
from unittest.mock import patch, MagicMock

import vllm.envs as envs
from vllm.engine.arg_utils import EngineArgs, _raise_or_fallback
from vllm.platforms.cuda import CudaPlatform
from vllm.config import ModelConfig


class TestSM75V1Compatibility:
    """测试SM75 GPU的V1架构兼容性"""

    def test_sm75_no_longer_raises_error(self):
        """测试SM75不再抛出NotImplementedError"""
        # 模拟SM75 GPU
        mock_capability = MagicMock()
        mock_capability.major = 7
        mock_capability.minor = 5

        with patch.object(CudaPlatform, 'is_cuda', return_value=True), \
             patch.object(CudaPlatform, 'get_device_capability', return_value=mock_capability):

            # 设置VLLM_USE_V1=1
            with patch.dict(os.environ, {'VLLM_USE_V1': '1'}):
                envs.set_vllm_use_v1(True)

                # 这应该不再抛出NotImplementedError
                args = EngineArgs(model="facebook/opt-125m")

                # 模拟_is_v1_supported_oracle检查
                model_config = args.create_model_config()
                result = args._is_v1_supported_oracle(model_config)

                # 应该返回True（不再因为SM75而失败）
                assert result is True

    def test_sm75_compatibility_warnings(self):
        """测试SM75的兼容性警告"""
        mock_capability = MagicMock()
        mock_capability.major = 7
        mock_capability.minor = 5

        with patch.object(CudaPlatform, 'is_cuda', return_value=True), \
             patch.object(CudaPlatform, 'get_device_capability', return_value=mock_capability), \
             patch('vllm.engine.arg_utils.logger') as mock_logger:

            with patch.dict(os.environ, {'VLLM_USE_V1': '1'}):
                envs.set_vllm_use_v1(True)

                args = EngineArgs(model="facebook/opt-125m")
                model_config = args.create_model_config()
                args._is_v1_supported_oracle(model_config)

                # 应该发出SM75兼容性警告
                mock_logger.warning.assert_called()
                warning_calls = [call for call in mock_logger.warning.call_args_list
                               if 'SM7.5' in str(call)]
                assert len(warning_calls) > 0

    def test_sm70_still_raises_error(self):
        """测试SM70仍然抛出错误（最低要求是SM70）"""
        mock_capability = MagicMock()
        mock_capability.major = 6
        mock_capability.minor = 1

        with patch.object(CudaPlatform, 'is_cuda', return_value=True), \
             patch.object(CudaPlatform, 'get_device_capability', return_value=mock_capability):

            with patch.dict(os.environ, {'VLLM_USE_V1': '1'}):
                envs.set_vllm_use_v1(True)

                args = EngineArgs(model="facebook/opt-125m")

                with pytest.raises(NotImplementedError, match="Compute Capability < 7.0"):
                    model_config = args.create_model_config()
                    args._is_v1_supported_oracle(model_config)

    def test_raise_or_fallback_sm75_handling(self):
        """测试_raise_or_fallback对SM75的特殊处理"""
        with patch.dict(os.environ, {'VLLM_USE_V1': '1'}), \
             patch('vllm.engine.arg_utils.logger') as mock_logger:

            envs.set_vllm_use_v1(True)

            # SM75应该只发出警告，不抛出异常
            _raise_or_fallback("Compute Capability < 8.0 (SM75)", recommend_to_remove=False)

            # 应该发出警告而不是抛出异常
            mock_logger.warning.assert_called_once()
            warning_call = mock_logger.warning.call_args[0][0]
            assert "SM7.5" in warning_call
            assert "limited support" in warning_call

    def test_sm80_plus_still_raises_error_for_other_features(self):
        """测试SM80+对其他不支持特性的错误处理"""
        with patch.dict(os.environ, {'VLLM_USE_V1': '1'}), \
             patch('vllm.engine.arg_utils.logger') as mock_logger:

            envs.set_vllm_use_v1(True)

            # 非SM75的特性应该仍然抛出错误
            with pytest.raises(NotImplementedError, match="not supported"):
                _raise_or_fallback("Some other unsupported feature", recommend_to_remove=False)

    def test_turing_backend_selection_sm75(self):
        """测试SM75正确选择Turing后端"""
        mock_capability = MagicMock()
        mock_capability.major = 7
        mock_capability.minor = 5

        with patch.object(CudaPlatform, 'is_cuda', return_value=True), \
             patch.object(CudaPlatform, 'get_device_capability', return_value=mock_capability), \
             patch.object(CudaPlatform, 'has_device_capability') as mock_has_capability:

            # 模拟不同硬件能力的检查
            def mock_has_capability_side_effect(capability, device_id=0):
                if capability == 80:
                    return False  # SM75不支持SM80特性
                elif capability == (7, 5):
                    return True   # 这是SM75
                elif capability == 75:
                    return True   # SM75检查
                return False

            mock_has_capability.side_effect = mock_has_capability_side_effect

            # 测试后端选择
            backend_cls = CudaPlatform.get_attn_backend_cls(
                selected_backend=None,
                head_size=64,
                dtype=torch.float16,
                kv_cache_dtype=None,
                block_size=16,
                use_v1=True,
                use_mla=False,
                has_sink=False
            )

            # 应该选择Turing V1后端
            assert "TuringAttentionBackendV1" in backend_cls

    def test_bfloat16_fallback_sm75(self):
        """测试SM75上BFloat16自动降级到Float16"""
        mock_capability = MagicMock()
        mock_capability.major = 7
        mock_capability.minor = 5

        with patch.object(CudaPlatform, 'is_cuda', return_value=True), \
             patch.object(CudaPlatform, 'get_device_capability', return_value=mock_capability), \
             patch.object(CudaPlatform, 'has_device_capability') as mock_has_capability:

            def mock_has_capability_side_effect(capability, device_id=0):
                if capability == 80:
                    return False  # SM75不支持SM80特性
                elif capability == (7, 5):
                    return True   # 这是SM75
                elif capability == 75:
                    return True   # SM75检查
                return False

            mock_has_capability.side_effect = mock_has_capability_side_effect

            # 测试BFloat16降级
            backend_cls = CudaPlatform.get_attn_backend_cls(
                selected_backend=None,
                head_size=64,
                dtype=torch.bfloat16,  # SM75不支持的数据类型
                kv_cache_dtype=None,
                block_size=16,
                use_v1=True,
                use_mla=False,
                has_sink=False
            )

            # 应该仍然能选择后端（降级处理）
            assert backend_cls is not None

    def test_fp8_kv_cache_fallback_sm75(self):
        """测试SM75上FP8 KV缓存自动降级"""
        mock_capability = MagicMock()
        mock_capability.major = 7
        mock_capability.minor = 5

        with patch.object(CudaPlatform, 'is_cuda', return_value=True), \
             patch.object(CudaPlatform, 'get_device_capability', return_value=mock_capability), \
             patch.object(CudaPlatform, 'has_device_capability') as mock_has_capability:

            def mock_has_capability_side_effect(capability, device_id=0):
                if capability == 80:
                    return False  # SM75不支持SM80特性
                elif capability == (7, 5):
                    return True   # 这是SM75
                elif capability == 75:
                    return True   # SM75检查
                return False

            mock_has_capability.side_effect = mock_has_capability_side_effect

            # 测试FP8 KV缓存降级
            backend_cls = CudaPlatform.get_attn_backend_cls(
                selected_backend=None,
                head_size=64,
                dtype=torch.float16,
                kv_cache_dtype="fp8",  # SM75不支持的KV缓存类型
                block_size=16,
                use_v1=True,
                use_mla=False,
                has_sink=False
            )

            # 应该仍然能选择后端（降级处理）
            assert backend_cls is not None


class TestSM75PerformanceWarnings:
    """测试SM75的性能警告"""

    def test_performance_warning_logged(self):
        """测试性能警告被正确记录"""
        mock_capability = MagicMock()
        mock_capability.major = 7
        mock_capability.minor = 5

        with patch.object(CudaPlatform, 'is_cuda', return_value=True), \
             patch.object(CudaPlatform, 'get_device_capability', return_value=mock_capability), \
             patch('vllm.engine.arg_utils.logger') as mock_logger:

            with patch.dict(os.environ, {'VLLM_USE_V1': '1'}):
                envs.set_vllm_use_v1(True)

                args = EngineArgs(model="facebook/opt-125m")
                model_config = args.create_model_config()
                args._is_v1_supported_oracle(model_config)

                # 应该有性能警告日志
                warning_calls = mock_logger.warning.call_args_list
                performance_warnings = [
                    call for call in warning_calls
                    if 'performance' in str(call).lower() or 'limited' in str(call).lower()
                ]
                assert len(performance_warnings) > 0

    def test_turing_backend_availability_check(self):
        """测试Turing后端可用性检查"""
        from vllm.attention.backends.turing_attn import is_turing_backend_available_for_v1

        mock_capability = MagicMock()
        mock_capability.major = 7
        mock_capability.minor = 5

        with patch.object(CudaPlatform, 'get_device_capability', return_value=mock_capability):
            # 模拟Turing后端可用
            with patch('vllm.attention.backends.turing_attn.TuringAttentionBackendV1'):
                available = is_turing_backend_available_for_v1()
                assert available is True


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])